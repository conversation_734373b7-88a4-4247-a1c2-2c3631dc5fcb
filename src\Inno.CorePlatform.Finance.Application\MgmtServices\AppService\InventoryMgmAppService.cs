using Dapr.Client;
using Inno.CorePlatform.Common.DDD;
using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.ApplicationServices.ApplicationInterfaces;
using Inno.CorePlatform.Finance.Application.CompetenceCenter.BDSCenter;
using Inno.CorePlatform.Finance.Application.DTOs;
using Inno.CorePlatform.Finance.Application.DTOs.Abatment;
using Inno.CorePlatform.Finance.Application.DTOs.InventoryRecord;
using Inno.CorePlatform.Finance.Domain.Enums;
using Inno.CorePlatform.Finance.Application.MgmtServices.Interfaces;
using Inno.CorePlatform.Finance.Application.PortInterfaces.Clients;
using Inno.CorePlatform.Finance.Application.QueryServices;
using Inno.CorePlatform.Finance.Data;
using Inno.CorePlatform.Finance.Data.Models;
using Inno.CorePlatform.Finance.Domain;
using Inno.CorePlatform.Finance.Domain.AggregateRoot;
using Inno.CorePlatform.Finance.Domain.AggregateRoot.Advance;
using Inno.CorePlatform.Finance.Domain.AggregateRoot.Credits;
using Inno.CorePlatform.Finance.Domain.AggregateRoot.RebateProvision;
using Inno.CorePlatform.Finance.Domain.AggregateRoot.Records;
using Inno.CorePlatform.Finance.Domain.PortInterfaces;
using Mapster;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace Inno.CorePlatform.Finance.Application.MgmtServices.AppService
{
    public class InventoryMgmAppService : IInventoryMgmAppService
    {
        private readonly ILogger<InventoryMgmAppService> _logger;
        private readonly IBDSApiClient _bDSApiClient;
        private readonly IBaseAllQueryService<InventoryItemPo> _inventoryQueryRep;
        private readonly IInventoryRepository _inventoryRepository;
        private readonly IAppServiceContextAccessor _appServiceContextAccessor;
        private readonly ISginyApiClient _sginyApiClient;
        private readonly ITinyApiClient _tinyApiClient;
        private readonly ITinyApiClientOfResponseData _tinyApiClientOfResponseData;
        private readonly IInventoryApiClient _inventoryApiClient;
        private readonly IInventoryApiClientOfResponseData _inventoryApiClientOfResponseData;
        private readonly ICreditRecordItemRepository _creditRecordItemRepository;
        private readonly IDebtRecordItemRepository _debtRecordItemRepository;
        private readonly IPaymentRecordItemRepository _paymentRecordItemRepository;
        private readonly IAdvanceRecordItemRepository _advanceRecordItemRepository;
        private readonly IAbatementRepository _abatementRepository;
        private readonly IBaseAllQueryService<AbatementPo> _queryAbatementPo;
        private readonly IBaseAllQueryService<CreditPo> _queryCreditPo;
        private readonly IBaseAllQueryService<DebtPo> _queryDebtPo;
        private readonly IBaseAllQueryService<PaymentPo> _queryPaymentPo;
        private readonly IBaseAllQueryService<CreditRecordItemPo> _queryCreditRecordItemPo;
        private readonly IBaseAllQueryService<DebtRecordItemPo> _queryDebtRecordItemPo;
        private readonly IBaseAllQueryService<PaymentRecordItemPo> _queryPaymentRecordItem;
        private readonly IBaseAllQueryService<AdvanceBusinessApplyPO> _queryAdvanceApplyPo;
        private readonly IBaseAllQueryService<AdvanceBusinessDetailPO> _queryAdvanceApplyDetailPo;
        //private readonly IBaseAllQueryService<AdvanceFundBusinessCheckDetailPO> _queryAdvanceRecordDetail;
        private readonly IBaseAllQueryService<AdvanceFundBusinessCheckItemPO> _queryAdvanceRecordItem;
        private readonly IBaseAllQueryService<RebateProvisionItemPo> _rebatProvisionItemQueryRep;
        private readonly IApplyBFFService _applyBFFService;
        private readonly IKingdeeApiClient _kingdeeApiClient;
        private readonly IPCApiClient _pCApiClient;
        private readonly DaprClient _daprClient;
        private readonly FinanceDbContext _db;
        private readonly IICApiClient _iCApiClient;
        private readonly IRebateProvisionItemRepository _rebateProvisionItemRepository;

        public InventoryMgmAppService(ILogger<InventoryMgmAppService> logger, IBDSApiClient bDSApiClient, IBaseAllQueryService<InventoryItemPo> inventoryQueryRep,
            IInventoryRepository inventoryRepository,
            IAppServiceContextAccessor appServiceContextAccessor,
            ISginyApiClient sginyApiClient,
            ITinyApiClient tinyApiClient,
            ITinyApiClientOfResponseData tinyApiClientOfResponseData,
            IInventoryApiClient inventoryApiClient,
            IInventoryApiClientOfResponseData inventoryApiClientOfResponseData,
            ICreditRecordItemRepository creditRecordItemRepository,
            IDebtRecordItemRepository debtRecordItemRepository,
            IPaymentRecordItemRepository paymentRecordItemRepository,
            IAbatementRepository abatementRepository,
            IBaseAllQueryService<AbatementPo> queryAbatementPo,
            IBaseAllQueryService<CreditPo> queryCreditPo,
            IBaseAllQueryService<DebtPo> queryDebtPo,
            IBaseAllQueryService<PaymentPo> queryPaymentPo,
            IBaseAllQueryService<CreditRecordItemPo> queryCreditRecordItemPo,
            IBaseAllQueryService<DebtRecordItemPo> queryDebtRecordItemPo,
            IBaseAllQueryService<PaymentRecordItemPo> queryPaymentRecordItem,
            IApplyBFFService applyBFFService,
            IKingdeeApiClient kingdeeApiClient,
            IPCApiClient pCApiClient,
            DaprClient daprClient,
            IBaseAllQueryService<AdvanceBusinessApplyPO> queryAdvanceApplyPo,
            IBaseAllQueryService<AdvanceBusinessDetailPO> queryAdvanceApplyDetailPo,
            IAdvanceRecordItemRepository advanceRecordItemRepository,
            IBaseAllQueryService<AdvanceFundBusinessCheckItemPO> queryAdvanceRecordItem,
            IBaseAllQueryService<RebateProvisionItemPo> rebatProvisionItemQueryRep,
            IICApiClient iCApiClient,
            FinanceDbContext db,
            IRebateProvisionItemRepository rebateProvisionItemRepository)
        {
            _logger = logger;
            _inventoryQueryRep = inventoryQueryRep;
            _bDSApiClient = bDSApiClient;
            _inventoryRepository = inventoryRepository;
            _appServiceContextAccessor = appServiceContextAccessor;
            _sginyApiClient = sginyApiClient;
            _tinyApiClient = tinyApiClient;
            _inventoryApiClient = inventoryApiClient;
            _creditRecordItemRepository = creditRecordItemRepository;
            _debtRecordItemRepository = debtRecordItemRepository;
            _paymentRecordItemRepository = paymentRecordItemRepository;
            _abatementRepository = abatementRepository;
            _queryAbatementPo = queryAbatementPo;
            _queryCreditPo = queryCreditPo;
            _queryDebtPo = queryDebtPo;
            _queryPaymentPo = queryPaymentPo;
            _applyBFFService = applyBFFService;
            _queryCreditRecordItemPo = queryCreditRecordItemPo;
            _queryDebtRecordItemPo = queryDebtRecordItemPo;
            _queryPaymentRecordItem = queryPaymentRecordItem;
            _kingdeeApiClient = kingdeeApiClient;
            _pCApiClient = pCApiClient;
            _daprClient = daprClient;
            _queryAdvanceApplyPo = queryAdvanceApplyPo;
            _queryAdvanceApplyDetailPo = queryAdvanceApplyDetailPo;
            _advanceRecordItemRepository = advanceRecordItemRepository;
            _queryAdvanceRecordItem = queryAdvanceRecordItem;
            _rebatProvisionItemQueryRep = rebatProvisionItemQueryRep;
            _rebateProvisionItemRepository = rebateProvisionItemRepository;
            _iCApiClient = iCApiClient;
            _db = db;
            _tinyApiClientOfResponseData = tinyApiClientOfResponseData;
            _inventoryApiClientOfResponseData = inventoryApiClientOfResponseData;
        }

        public async Task<(List<InventoryDTO>, int)> GetInventoryList(InventoryQueryDto queryDto)
        {
            // 使用提取的验证方法替代原来的内联验证逻辑
            var hasPermission = await ValidateUserPermissionAsync(queryDto.UserId, "metadata://fam");
            if (!hasPermission)
            {
                return (new List<InventoryDTO>(), 0);
            }

            // 重新获取策略信息用于后续的权限过滤
            var input = new StrategyQueryInput() { userId = queryDto.UserId, functionUri = "metadata://fam" };
            var strategry = await _pCApiClient.GetStrategyAsync(input);
            var query = _inventoryQueryRep.GetIQueryable(p => true);
            if (!queryDto.PageIndex.HasValue || queryDto.PageIndex == 0)
            {
                queryDto.PageIndex = 1;
            }
            if (!queryDto.PageSize.HasValue || queryDto.PageSize == 0)
            {
                queryDto.PageSize = 20;
            }
            if (queryDto.Status.HasValue)
            {
                query = query.Where(p => p.Status == queryDto.Status);
            }
            if (!string.IsNullOrEmpty(queryDto.SysMonth))
            {
                query = query.Where(p => EF.Functions.Like(p.SysMonth, $"%{queryDto.SysMonth}%"));
            }
            if (queryDto.CompanyId.HasValue)
            {
                query = query.Where(p => p.CompanyId == queryDto.CompanyId.Value);
            }
            else
            {

                if (strategry != null && strategry.RowStrategies.Any())
                {
                    foreach (var key in strategry.RowStrategies.Keys)
                    {
                        if (key == "company")
                        {
                            if (!strategry.RowStrategies[key].Any(z => z == "@all"))
                            {
                                var strategList = strategry.RowStrategies[key].ToGuidHashSet();
                                query = query.Where(t => strategList.Contains(t.CompanyId));
                            }
                            break;
                        }
                    }
                }
            }
            var total = await query.CountAsync();
            var list = await query.OrderByDescending(p => p.SysMonth).ThenBy(p => p.CompanyName).Skip((queryDto.PageIndex.Value - 1) * queryDto.PageSize.Value).Take(queryDto.PageSize.Value).ToListAsync();
            var res = list.Adapt<List<InventoryDTO>>();
            return (res, total);
        }

        public async Task<InventoryDTO?> GetInventoryByCompanyAndMonth(Guid companyId, string sysMonth)
        {
            var query = _inventoryQueryRep.GetIQueryable(p => p.CompanyId == companyId && p.SysMonth == sysMonth);
            var item = await query.FirstOrDefaultAsync();
            return item?.Adapt<InventoryDTO>();
        }

        public async Task<List<InventoryDTO>> GetInventoriesByCompaniesAndMonth(List<Guid> companyIds, string sysMonth)
        {
            if (companyIds.Count == 0)
            {
                return [];
            }

            _logger.LogInformation("批量获取盘点记录 - 系统月度: {SysMonth}, 公司数量: {Count}", sysMonth, companyIds.Count);

            var query = _inventoryQueryRep.GetIQueryable(p => companyIds.Contains(p.CompanyId) && p.SysMonth == sysMonth);
            var items = await query.ToListAsync();
            var result = items.Adapt<List<InventoryDTO>>();

            _logger.LogInformation("批量获取盘点记录完成 - 系统月度: {SysMonth}, 请求: {RequestCount}, 获取: {ResultCount}",
                sysMonth, companyIds.Count, result.Count);

            return result;
        }

        public async Task UpdateInventoryStatus(Guid inventoryId, int status, string? updatedBy = null)
        {
            try
            {
                // 使用Repository的UpdateSpecificFields方法一次性更新所有字段，避免实体跟踪冲突
                var fieldUpdates = new Dictionary<string, string>
                {
                    { "Status", status.ToString() }
                };

                // 通过Repository的UpdateSpecificFields方法更新，该方法内部会处理UpdatedTime
                var affectedRows = await _inventoryRepository.UpdateSpecificFields(inventoryId, fieldUpdates);

                if (affectedRows > 0)
                {
                    // 记录更新成功信息
                    _logger.LogInformation("盘点状态更新成功 - 盘点ID: {InventoryId}, 状态: {Status}, 更新者: {UpdatedBy}",
                        inventoryId, status, updatedBy ?? "系统");
                }
                else
                {
                    _logger.LogWarning("更新盘点状态失败，未找到记录 - 盘点ID: {InventoryId}", inventoryId);
                    throw new InvalidOperationException($"盘点记录不存在，ID: {inventoryId}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新盘点状态失败 - 盘点ID: {InventoryId}, 状态: {Status}, 错误: {Message}",
                    inventoryId, status, ex.Message);
                throw;
            }
        }

        public async Task<int> BatchUpdateInventoryStatus(List<Guid> inventoryIds, int status, string? updatedBy = null)
        {
            if (inventoryIds.Count == 0)
            {
                return 0;
            }

            try
            {
                _logger.LogInformation("批量更新盘点状态 - 盘点数量: {Count}, 状态: {Status}, 更新者: {UpdatedBy}",
                    inventoryIds.Count, status, updatedBy ?? "系统");

                // 使用EF Core的批量更新
                var totalAffectedRows = 0;
                const int batchSize = 100; // 每批处理100个，避免SQL参数过多

                for (int i = 0; i < inventoryIds.Count; i += batchSize)
                {
                    var currentBatch = inventoryIds.Skip(i).Take(batchSize).ToList();

                    // 批量更新当前批次的盘点状态
                    var batchAffectedRows = await _db.InventoryItem
                        .Where(x => currentBatch.Contains(x.Id))
                        .ExecuteUpdateAsync(setters => setters
                            .SetProperty(x => x.Status, status)
                            .SetProperty(x => x.UpdatedTime, DateTime.Now));

                    totalAffectedRows += batchAffectedRows;

                    _logger.LogInformation("批量更新盘点状态 - 批次: {BatchIndex}, 当前批次: {CurrentBatchSize}, 更新成功: {AffectedRows}",
                        (i / batchSize) + 1, currentBatch.Count, batchAffectedRows);
                }

                _logger.LogInformation("批量更新盘点状态完成 - 总数: {Total}, 成功: {Success}, 状态: {Status}",
                    inventoryIds.Count, totalAffectedRows, status);

                return totalAffectedRows;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "批量更新盘点状态异常 - 盘点数量: {Count}, 状态: {Status}, 错误: {Message}",
                    inventoryIds.Count, status, ex.Message);
                throw;
            }
        }

        public async Task<BaseResponseData<int>> StartInventory()
        {
            var sysMonth = DateTime.Now.ToString("yyyy-MM");

            // 使用提取的验证方法替代原来的内联验证逻辑
            var validationResult = await ValidateStartInventoryAsync(sysMonth);
            if (!validationResult.IsValid)
            {
                return BaseResponseData<int>.Failed(0, validationResult.ErrorMessage);
            }
            var companys = await _bDSApiClient.GetCompanyInfoAsync(new CompetenceCenter.BDSCenter.BDSBaseInput() { });
            var inventoryItems = new List<InventoryDTO>();
            companys = companys.Where(p => !string.IsNullOrEmpty(p.sysMonth)).ToList();
            var ids = companys.Select(p => Guid.Parse(p.companyId)).ToList();
            var noStoreRoomCompanys = await _inventoryApiClient.GetNoStoreRoomCompanys(ids);
            var rebateProvisionItems = new List<RebateProvisionItem>();
            companys.ForEach(company =>
            {
                if (company.sysMonth == DateTime.Now.Date.ToString("yyyy-MM"))
                {
                    var item = new InventoryDTO()
                    {
                        Id = Guid.NewGuid(),
                        CompanyId = Guid.Parse(company.companyId),
                        CompanyLongName = company.companyName,
                        CompanyName = company.companyShortName,
                        SysMonth = company.sysMonth,
                        Status = 0,
                        CreatedBy = _appServiceContextAccessor.Get().UserName,
                        CreatedTime = DateTime.Now,
                        Exchange = "",
                        Operation = "",
                        Store = "",
                        TempStore = "",
                        ThirdStore = "",
                    };
                    if (noStoreRoomCompanys.Any(p => p == item.CompanyId))
                    {
                        item.Status = 2;
                        item.Store = "[]";
                    }
                    inventoryItems.Add(item);

                    rebateProvisionItems.Add(new RebateProvisionItem()
                    {
                        BillDate = DateTime.Now.Date,
                        CompanyId = Guid.Parse(company.companyId),
                        CompanyName = company.companyName,
                        CreatedTime = DateTimeOffset.Now,
                        Status = StatusEnum.waitSubmit,
                        CreatedBy = item.CreatedBy,
                        Id = Guid.NewGuid(),
                        BillCode = company.nameCode + $"-{DateTime.Now.ToString("yyMM")}-JT-0001",
                        NameCode = company.nameCode,
                        ProvisionType = ProvisionTypeEnum.EndMonth
                    });
                }
            });
            var toAddList = inventoryItems.Adapt<List<InventoryItem>>();
            var inventoryItemPos = toAddList.Adapt<List<InventoryItemPo>>();
            if (inventoryItemPos.Any() && inventoryItemPos.Count > 0)
            {
                await _db.InventoryItem.AddRangeAsync(inventoryItemPos);
            }
            if (rebateProvisionItems.Any() && rebateProvisionItems.Count > 0)
            {
                var rebateProvisionItemCodes = rebateProvisionItems.Select(p => p.BillCode).ToList();
                var rebateProvisionItemPos = await _db.RebateProvisionItem.Where(p => rebateProvisionItemCodes.Contains(p.BillCode)).ToListAsync();
                if (rebateProvisionItemPos.Any() && rebateProvisionItemPos.Count() > 0)
                {
                    rebateProvisionItemCodes = rebateProvisionItemPos.Select(p => p.BillCode).ToList();
                    rebateProvisionItems = rebateProvisionItems.Where(p => !rebateProvisionItemCodes.Contains(p.BillCode)).ToList();
                }
                var rebateProvisionItems1 = rebateProvisionItems.Adapt<List<RebateProvisionItemPo>>();
                await _db.RebateProvisionItem.AddRangeAsync(rebateProvisionItems1);
            }
            await _db.SaveChangesAsync();
            return BaseResponseData<int>.Success("启动盘点成功");

        }

        public async Task<BaseResponseData<int>> UpdateStoreInventory(StoreInventoryUpdateInputDto dto)
        {
            //var item = await _inventoryRepository.GetUnFinishInventoryItem(dto.CompanyId);
            var query = _inventoryQueryRep.GetIQueryable(p => p.Status < 2 && p.CompanyId == dto.CompanyId);
            var itemPo = await query.FirstOrDefaultAsync();
            if (itemPo == null)
            {
                return BaseResponseData<int>.Failed(0, "该公司总盘点单不存在或不在库存盘点状态");
            }
            var item = itemPo.Adapt<InventoryItem>();
            var store = item.Store;
            if (string.IsNullOrEmpty(store))
            {
                var thisData = new List<StoreInventoryUpdateInputDto>() { dto };
                item.Store = JsonConvert.SerializeObject(thisData);
                item.Status = 1;
                await _inventoryRepository.UpdateAsync(item);
                return BaseResponseData<int>.Success("操作成功");
            }
            else
            {
                var data = JsonConvert.DeserializeObject<List<StoreInventoryUpdateInputDto>>(store);
                var thisStore = data.Where(p => p.StoreHouseId == dto.StoreHouseId).FirstOrDefault();
                if (thisStore == null)
                {
                    data.Add(dto);
                    item.Store = JsonConvert.SerializeObject(data);
                    item.Status = 1;
                    await _inventoryRepository.UpdateAsync(item);
                    return BaseResponseData<int>.Success("操作成功");
                }
                else
                {
                    thisStore.StoreCheckStatus = dto.StoreCheckStatus;
                    if (!data.Any(p => p.StoreCheckStatus != true) && data.Count == data.FirstOrDefault().Total)
                    {
                        item.Status = 2;
                    }
                    item.Store = JsonConvert.SerializeObject(data);
                    await _inventoryRepository.UpdateAsync(item);
                    return BaseResponseData<int>.Success("操作成功");
                }
            }
        }

        public async Task<BaseResponseData<List<OutInventoryItemOutput>>> GetNoFinishBillForInventory(OutInventoryItemInput input)
        {
            var ret = BaseResponseData<List<OutInventoryItemOutput>>.Success("操作成功");
            var inventoryItems = await _inventoryQueryRep.GetAllListAsync(p => p.Status != 99 && input.CompanyIds.Contains(p.CompanyId));
            ret.Data = inventoryItems.Select(p => new OutInventoryItemOutput
            {
                CompanyId = p.CompanyId,
                SysMonth = p.SysMonth,
            }).ToList();
            return ret;
        }

        public async Task<BaseResponseData<InventoryInfoForCompanyDto>> GetInventoryStatus(Guid companyId)
        {
            var query = _inventoryQueryRep.GetIQueryable(p => p.CompanyId == companyId);
            var inventoryItem = await query.OrderByDescending(o => o.SysMonth).FirstOrDefaultAsync();
            if (inventoryItem == null)
            {
                return new BaseResponseData<InventoryInfoForCompanyDto>()
                {
                    Code = CodeStatusEnum.Success,
                    Data = new InventoryInfoForCompanyDto()
                    {
                        CompanyId = companyId,
                        InventoryStatus = -1,
                        StoreCheckInfo = null
                    }
                };
            }
            else
            {
                var res = new InventoryInfoForCompanyDto();
                res.InventoryStatus = inventoryItem.Status;
                res.CompanyId = companyId;
                return new BaseResponseData<InventoryInfoForCompanyDto>()
                {
                    Code = CodeStatusEnum.Success,
                    Data = res
                };
            }
        }

        public async Task<BaseResponseData<int>> CreateOtherCheck(Guid companyId, Guid userId, string userName)
        {
            // 使用提取的验证方法替代原来的内联验证逻辑
            var validationResult = await ValidateCreateOtherCheckAsync(companyId);
            if (!validationResult.IsValid)
            {
                return BaseResponseData<int>.Failed(0, validationResult.ErrorMessage);
            }

            // 获取必要的数据（这部分逻辑保持不变）
            var query = _inventoryQueryRep.GetIQueryable(p => p.CompanyId == companyId && p.Status != 99);
            var itemPo = await query.FirstOrDefaultAsync();
            var item = itemPo.Adapt<InventoryItem>();
            var _CompanyList = await _applyBFFService.GetCompanyInfosAsync(new BDSBaseInput() { ids = new List<string> { companyId.ToString() } });
            var currentSysMonth = item.SysMonth; // 从当前盘点单获取系统月度
            var companyInfo = _CompanyList.FirstOrDefault();

            // 构建需要执行的盘点动作列表
            var actions = new List<InventoryActionDto>();

            // 1. 暂存盘点
            if (string.IsNullOrEmpty(item.TempStore))
            {
                actions.Add(new InventoryActionDto
                {
                    ActionType = InventoryActionType.CreateTinyInventory,
                    ActionName = "创建暂存盘点",
                    NeedExecute = true,
                    Order = 1
                });
            }

            // 2. 跟台盘点
            if (string.IsNullOrEmpty(item.Operation))
            {
                actions.Add(new InventoryActionDto
                {
                    ActionType = InventoryActionType.CreateSginyInventory,
                    ActionName = "创建跟台盘点",
                    NeedExecute = true,
                    Order = 2
                });
            }

            // 3. 换货盘点
            if (string.IsNullOrEmpty(item.Exchange))
            {
                actions.Add(new InventoryActionDto
                {
                    ActionType = InventoryActionType.CreateExchangeInventory,
                    ActionName = "创建换货盘点",
                    NeedExecute = true,
                    Order = 3
                });
            }

            // 4. 待确认收入盘点
            if (string.IsNullOrEmpty(item.SureIncomeCode))
            {
                actions.Add(new InventoryActionDto
                {
                    ActionType = InventoryActionType.CreateSureIncomeInventory,
                    ActionName = "创建待确认收入盘点",
                    NeedExecute = true,
                    Order = 4
                });
            }

            // 5. 应收盘点
            if (string.IsNullOrEmpty(item.CreditRecordCode))
            {
                actions.Add(new InventoryActionDto
                {
                    ActionType = InventoryActionType.CreateCreditRecordInventory,
                    ActionName = "创建应收盘点",
                    NeedExecute = true,
                    Order = 5
                });
            }

            // 6. 已签收待开票盘点
            if (string.IsNullOrEmpty(item.ReceivedNoInvoiceRecordCode))
            {
                actions.Add(new InventoryActionDto
                {
                    ActionType = InventoryActionType.CreateReceivedNoInvoiceInventory,
                    ActionName = "创建已签收待开票盘点",
                    NeedExecute = true,
                    Order = 6
                });
            }

            // 7. 应付盘点
            if (string.IsNullOrEmpty(item.DebtRecordCode))
            {
                actions.Add(new InventoryActionDto
                {
                    ActionType = InventoryActionType.CreateDebtRecordInventory,
                    ActionName = "创建应付盘点",
                    NeedExecute = true,
                    Order = 7
                });
            }

            // 8. 付款盘点
            if (string.IsNullOrEmpty(item.PaymentRecordCode))
            {
                actions.Add(new InventoryActionDto
                {
                    ActionType = InventoryActionType.CreatePaymentRecordInventory,
                    ActionName = "创建付款盘点",
                    NeedExecute = true,
                    Order = 8
                });
            }

            // 9. 垫资盘点
            if (string.IsNullOrEmpty(item.AdvanceRecordCode))
            {
                actions.Add(new InventoryActionDto
                {
                    ActionType = InventoryActionType.CreateAdvanceRecordInventory,
                    ActionName = "创建垫资盘点",
                    NeedExecute = true,
                    Order = 9
                });
            }

            // 创建统一的盘点事件
            var createOtherCheckEvent = new CreateOtherCheckEventDto
            {
                CompanyId = companyId,
                InventoryItemId = item.Id,
                Status = item.Status,
                SysMonth = currentSysMonth, // 使用当前盘点单的系统月度
                UserId = userId,
                UserName = userName,
                CompanyCode = companyInfo?.NameCode,
                CompanyName = companyInfo?.Name,
                Actions = actions
            };

            try
            {
                // 发布统一的创建其他盘点事件
                await _daprClient.PublishEventAsync(DomainConstants.Default_PubSubName, "finance-inventory-createothercheck", createOtherCheckEvent);

                // 发布成功后，将对应的code值修改为"生成中"
                var fieldUpdates = new Dictionary<string, string>();
                foreach (var action in actions)
                {
                    switch (action.ActionType)
                    {
                        case InventoryActionType.CreateTinyInventory:
                            fieldUpdates["TempStore"] = "生成中";
                            break;
                        case InventoryActionType.CreateSginyInventory:
                            fieldUpdates["Operation"] = "生成中";
                            break;
                        case InventoryActionType.CreateExchangeInventory:
                            fieldUpdates["Exchange"] = "生成中";
                            break;
                        case InventoryActionType.CreateSureIncomeInventory:
                            fieldUpdates["SureIncomeCode"] = "生成中";
                            break;
                        case InventoryActionType.CreateCreditRecordInventory:
                            fieldUpdates["CreditRecordCode"] = "生成中";
                            break;
                        case InventoryActionType.CreateReceivedNoInvoiceInventory:
                            fieldUpdates["ReceivedNoInvoiceRecordCode"] = "生成中";
                            break;
                        case InventoryActionType.CreateDebtRecordInventory:
                            fieldUpdates["DebtRecordCode"] = "生成中";
                            break;
                        case InventoryActionType.CreatePaymentRecordInventory:
                            fieldUpdates["PaymentRecordCode"] = "生成中";
                            break;
                        case InventoryActionType.CreateAdvanceRecordInventory:
                            fieldUpdates["AdvanceRecordCode"] = "生成中";
                            break;
                    }
                }

                // 批量更新字段
                if (fieldUpdates.Any())
                {
                    await UpdateInventoryItemFields(item.Id, fieldUpdates);
                }

                _logger.LogInformation($"发布创建 其他盘点 事件成功，公司ID: {companyId}, 事件ID: {createOtherCheckEvent.EventId}, 待执行动作数量: {actions.Count}");

                return BaseResponseData<int>.Success($"盘点事件 已发布，共有 {actions.Count} 个盘点动作将异步处理");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"发布创建 其他盘点 事件失败，公司ID: {companyId}");
                return BaseResponseData<int>.Failed(500, "发布盘点事件 失败，请稍后重试");
            }
        }

        /// <summary>
        /// 应收盘点
        /// </summary>
        /// <param name="companyId">公司id</param>
        /// <param name="sysmonth">盘底月份</param>
        /// <param name="compnyCode">公司Code</param>
        /// <param name="compnyName">公司名称</param>
        /// <param name="userName">操作用户</param>
        /// <returns></returns>
        public async Task<(int, string)> CreditRecordInventory(Guid companyId, string sysmonth, string compnyCode, string compnyName, string userName)
        {
            int excuteCount = 0;
            string code = GetCodeInventory(compnyCode, "CR", sysmonth);

            _logger.LogInformation("开始应收盘点 - 公司: {CompanyId}, 月度: {SysMonth}, 盘点单号: {Code}", companyId, sysmonth, code);

            // 检查是否已存在相同的盘点记录
            var checkRepeat = await _queryCreditRecordItemPo.FirstOrDefaultAsync(x => x.Code == code && x.CompanyId == companyId);
            if (checkRepeat != null)
            {
                _logger.LogInformation("应收盘点记录 已存在 - 公司: {CompanyId}, 盘点单号: {Code}", companyId, code);
                return (1, code);
            }

            // 查询符合条件的应收数据
            var creditQuery = _queryCreditPo.GetIQueryable(x => x.AbatedStatus == AbatedStatusEnum.NonAbate && x.CompanyId == companyId);
            var creditList = await creditQuery.ToListAsync();

            _logger.LogInformation("查询到应收数据 - 公司: {CompanyId}, 数据量: {Count}", companyId, creditList.Count);

            if (!creditList.Any())
            {
                _logger.LogWarning("未找到符合条件的应收数据 - 公司: {CompanyId}, 跳过应收盘点 创建", companyId);
            }

            // 查询核销数据
            var billCodeQuery = creditList.Select(x => x.BillCode).ToList();
            var abateMentQuery = _queryAbatementPo.GetIQueryable(x => billCodeQuery.Contains(x.DebtBillCode) || billCodeQuery.Contains(x.CreditBillCode));
            var abateMentList = await abateMentQuery.ToListAsync();
            var abateMentDTOList = abateMentList.Select(p => p.Adapt<AbatmentDTO>()).ToList();

            _logger.LogInformation("查询到核销数据 - 公司: {CompanyId}, 数据量: {Count}", companyId, abateMentDTOList.Count);

            // 创建盘点单头
            var CreditRecordItem = new CreditRecordItem
            {
                Id = Guid.NewGuid(),
                BillDate = GetBillDateInventory(sysmonth),
                Code = code,
                CompanyId = companyId,
                CompanyName = compnyName,
                UserName = userName,
                Operator = "",
                Classify = CreditRecordItemClassifyEnum.Credit,
            };
            CreditRecordItem.CreateBy(userName);

            // 创建盘点明细
            List<CreditRecordDetail> CreditRecordDetailList = new List<CreditRecordDetail>();

            foreach (var item in creditList)
            {
                var abateMentItem = abateMentDTOList.Where(x => x.DebtBillCode == item.BillCode || x.CreditBillCode == item.BillCode).ToList();
                var abatedValue = abateMentItem.Sum(t => t.Value);

                // 使用统一的余额计算公式：余额 = 应收金额绝对值 - 冲销金额
                var calculatedBalance = CalculateUnifiedCreditBalance(item.Value, abatedValue);

                var CreditRecordDetail = new CreditRecordDetail
                {
                    CreditId = item.Id,
                    CreditRecordItemId = CreditRecordItem.Id,
                    AbatedValue = abatedValue,
                    Value = calculatedBalance,
                    IsLongTerm = item.IsLongTerm ?? 0,
                };

                CreditRecordDetail.CreateBy(userName);
                CreditRecordDetailList.Add(CreditRecordDetail);
            }

            _logger.LogInformation("创建应收盘点 明细 - 公司: {CompanyId}, 明细数量: {Count}", companyId, CreditRecordDetailList.Count);

            try
            {
                excuteCount = await _creditRecordItemRepository.InsetrCredit(CreditRecordItem, CreditRecordDetailList);
                _logger.LogInformation("应收盘点数据 插入完成 - 公司: {CompanyId}, 影响行数: {ExecuteCount}, 盘点单号: {Code}",
                    companyId, excuteCount, code);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "应收盘点数据 插入失败 - 公司: {CompanyId}, 盘点单号: {Code}, 错误: {Message}",
                    companyId, code, ex.Message);
                throw;
            }

            return (excuteCount, code);
        }
        /// <summary>
        /// 已签收待开票盘点（应收盘点）
        /// </summary>
        /// <param name="companyId">公司id</param>
        /// <param name="sysmonth">盘底月份</param>
        /// <param name="compnyCode">公司Code</param>
        /// <param name="compnyName">中文名</param>
        /// <param name="userName">拼音名字</param>
        /// <returns></returns>
        public async Task<(int, string)> ReceivedNoInvoiceInventory(Guid companyId, string sysmonth, string compnyCode, string compnyName, string userName)
        {
            int excuteCount = 0;
            string code = GetCodeInventory(compnyCode, "RR", sysmonth);

            var checkRepeat = await _queryCreditRecordItemPo.FirstOrDefaultAsync(x => x.Code == code && x.CompanyId == companyId);
            if (checkRepeat != null)
            {
                return (1, code);
            }

            // 获取应收数据，排除已冲销的记录
            var creditBaseQuery = _queryCreditPo.GetIQueryable(x => x.CompanyId == companyId);

            // 优化：获取已开票金额，添加索引提示和更精确的查询条件
            var invoiceCredits = await _db.InvoiceCredits
                .Where(ic => ic.CreditId != null && ic.IsCancel != true)
                .GroupBy(ic => ic.CreditId.Value)
                .Select(g => new { CreditId = g.Key, InvoiceAmount = g.Sum(x => x.CreditAmount ?? 0) })
                .ToListAsync();

            // 优化：使用容量预分配，提高字典性能
            var invoiceAmountDict = new Dictionary<Guid, decimal>(invoiceCredits.Count);
            foreach (var item in invoiceCredits)
            {
                invoiceAmountDict[item.CreditId] = item.InvoiceAmount;
            }

            // 获取所有应收记录到内存
            var allCredits = await creditBaseQuery.ToListAsync();

            // 优化：根据新的三个条件筛选应收记录（使用OR逻辑），优化性能
            var creditQuery = allCredits.Where(credit =>
            {
                // 条件1：应收单余额≠0（最常见的条件，优先判断）
                if (credit.Value != 0) return true;

                // 条件2：确认收入金额≠应收单金额（IsSureIncome=0表示未确认收入）
                if (credit.IsSureIncome == 0) return true;

                // 条件3：应收单开票金额≠应收单金额（最后判断，因为需要字典查找）
                // 如果应收是无需开票，则认为开票金额等于应收金额，直接跳过此条件
                if (credit.IsNoNeedInvoice == IsNoNeedInvoiceEnum.NoNeed)
                {
                    return false; // 无需开票的应收，开票金额视为等于应收金额，此条件不满足
                }
                else
                {
                    // 需要开票的应收，检查实际开票金额是否等于应收金额
                    var invoiceAmount = invoiceAmountDict.GetValueOrDefault(credit.Id, 0);
                    return invoiceAmount != credit.Value;
                }
            }).ToList();

            // 优化：提前检查是否有数据，避免不必要的处理
            if (!creditQuery.Any())
            {
                _logger.LogWarning("筛选后无符合条件的应收数据 - 公司: {CompanyId}", companyId);
            }
            else
            {
                _logger.LogInformation("已签收待开票盘点筛选完成 - 公司: {CompanyId}, 原始应收数量: {OriginalCount}, 筛选后数量: {FilteredCount}",
                    companyId, allCredits.Count, creditQuery.Count);
            }

            // 优化：使用HashSet提高查询性能，过滤空值
            var billCodes = creditQuery
                .Where(x => !string.IsNullOrEmpty(x.BillCode))
                .Select(x => x.BillCode)
                .ToHashSet();

            // 优化：使用分批查询或临时表方案处理大量单号查询
            _logger.LogInformation("开始查询核销数据 - 公司: {CompanyId}, 应收记录数量: {CreditCount}", companyId, creditQuery.Count);

            var billCodeSet = creditQuery.Where(x => !string.IsNullOrEmpty(x.BillCode))
                                        .Select(x => x.BillCode!)
                                        .ToHashSet();

            ILookup<string, AbatmentDTO> abateMentLookup;

            if (billCodeSet.Count <= 2000) // 小于2000个单号，直接查询
            {
                _logger.LogInformation("单号数量较少({Count})，使用直接查询", billCodeSet.Count);
                var abateMentQuery = _queryAbatementPo.GetIQueryable(x =>
                    billCodeSet.Contains(x.DebtBillCode) || billCodeSet.Contains(x.CreditBillCode));
                var abateMentList = await abateMentQuery.ToListAsync();
                var abateMentDTOList = abateMentList.Select(p => p.Adapt<AbatmentDTO>()).ToList();

                abateMentLookup = abateMentDTOList
                    .SelectMany(x => new[]
                    {
                        new { BillCode = x.DebtBillCode, Abatement = x },
                        new { BillCode = x.CreditBillCode, Abatement = x }
                    })
                    .Where(x => !string.IsNullOrEmpty(x.BillCode))
                    .ToLookup(x => x.BillCode, x => x.Abatement);
            }
            else // 大量单号，使用分批查询
            {
                _logger.LogInformation("单号数量较多({Count})，使用分批查询", billCodeSet.Count);
                abateMentLookup = await QueryAbatementInBatchesAsync(billCodeSet.ToList());
            }

            // 创建盘点单头
            var CreditRecordItem = new CreditRecordItem
            {
                Id = Guid.NewGuid(),
                BillDate = GetBillDateInventory(sysmonth),
                Code = code,
                CompanyId = companyId,
                CompanyName = compnyName,
                UserName = userName,
                Operator = "",
                Classify = CreditRecordItemClassifyEnum.SignedNonInvoiced,
            };
            CreditRecordItem.CreateBy(userName);

            // 优化：预分配容量，减少List扩容开销
            var CreditRecordDetailList = new List<CreditRecordDetail>(creditQuery.Count);

            // 优化：批量处理，减少重复的字符串操作和查找
            foreach (var item in creditQuery)
            {
                // 优化：直接使用空字符串作为默认值，避免多次null检查
                var billCode = item.BillCode ?? string.Empty;
                var abateMentItems = abateMentLookup[billCode];
                var abatedValue = abateMentItems.Sum(t => t.Value);

                // 使用统一的余额计算公式：余额 = 应收金额绝对值 - 冲销金额
                var calculatedValue = CalculateUnifiedCreditBalance(item.Value, abatedValue);

                var CreditRecordDetail = new CreditRecordDetail
                {
                    CreditId = item.Id,
                    CreditRecordItemId = CreditRecordItem.Id,
                    AbatedValue = abatedValue,
                    Value = calculatedValue,
                    IsLongTerm = item.IsLongTerm ?? 0,
                };

                CreditRecordDetail.CreateBy(userName);
                CreditRecordDetailList.Add(CreditRecordDetail);
            }

            if (!CreditRecordDetailList.Any())
            {
                _logger.LogWarning("未找到符合条件的应收盘点数据 - 公司: {CompanyId}, 跳过应收盘点 创建", companyId);
            }

            excuteCount = await _creditRecordItemRepository.InsetrCredit(CreditRecordItem, CreditRecordDetailList);
            return (excuteCount, code);
        }

        /// <summary>
        /// 应付盘点
        /// </summary>
        /// <param name="companyId">公司id</param>
        /// <param name="sysmonth">盘底月份</param>
        /// <param name="compnyCode">公司Code</param>
        /// <param name="compnyName">中文名</param>
        /// <param name="userName">拼音名字</param>
        /// <returns></returns>
        public async Task<(int, string)> DebtRecordInventory(Guid companyId, string sysmonth, string compnyCode, string compnyName, string userName)
        {
            int excuteCount = 0;
            string code = GetCodeInventory(compnyCode, "DR", sysmonth);

            _logger.LogInformation("开始应付盘点 - 公司: {CompanyId}, 月度: {SysMonth}, 盘点单号: {Code}", companyId, sysmonth, code);

            // 检查是否已存在相同的盘点记录
            var checkRepeat = await _queryDebtRecordItemPo.FirstOrDefaultAsync(x => x.Code == code && x.CompanyId == companyId);
            if (checkRepeat != null)
            {
                _logger.LogInformation("应付盘点记录 已存在 - 公司: {CompanyId}, 盘点单号: {Code}", companyId, code);
                return (1, code);
            }

            // 查询符合条件的应付数据
            var debtQuery = _queryDebtPo.GetIQueryable(x => x.CompanyId == companyId);
            var debtList = await debtQuery.ToListAsync();

            _logger.LogInformation("查询到应付数据 - 公司: {CompanyId}, 数据量: {Count}", companyId, debtList.Count);

            if (!debtList.Any())
            {
                _logger.LogWarning("未找到符合条件的应付数据 - 公司: {CompanyId}, 跳过应付盘点 创建", companyId);
            }

            // 优化：智能选择查询策略，避免大量单号Contains查询
            var billCodeSet = debtList.Where(x => !string.IsNullOrEmpty(x.BillCode))
                                     .Select(x => x.BillCode!)
                                     .ToHashSet();

            List<AbatmentDTO> abateMentDTOList;
            if (billCodeSet.Count <= 2000) // 小于2000个单号，直接查询
            {
                var abateMentQuery = _queryAbatementPo.GetIQueryable(x =>
                    billCodeSet.Contains(x.DebtBillCode) || billCodeSet.Contains(x.CreditBillCode));
                var abateMentList = await abateMentQuery.ToListAsync();
                abateMentDTOList = abateMentList.Select(p => p.Adapt<AbatmentDTO>()).ToList();
            }
            else // 大量单号，使用分批查询
            {
                var abateMentLookup = await QueryAbatementInBatchesAsync(billCodeSet.ToList());
                abateMentDTOList = abateMentLookup.SelectMany(g => g).ToList();
            }

            _logger.LogInformation("查询到核销数据 - 公司: {CompanyId}, 数据量: {Count}", companyId, abateMentDTOList.Count);

            // 创建盘点单头
            var DebtRecordItem = new DebtRecordItem
            {
                Id = Guid.NewGuid(),
                BillDate = GetBillDateInventory(sysmonth),
                Code = code,
                CompanyId = companyId,
                CompanyName = compnyName,
                UserName = userName,
                Operator = ""
            };
            DebtRecordItem.CreateBy(userName);

            // 创建盘点明细
            List<DebtRecordDetail> DebtRecordDetailList = new List<DebtRecordDetail>();
            int filteredCount = 0;

            foreach (var item in debtList)
            {
                var abateMentItem = abateMentDTOList.Where(x => x.DebtBillCode == item.BillCode || x.CreditBillCode == item.BillCode).ToList();
                var abatedValue = abateMentItem.Sum(t => t.Value);

                // 只有当核销金额不等于应付金额时才添加明细（即还有未核销余额）
                if (abatedValue != item.Value)
                {
                    var DebtRecordDetail = new DebtRecordDetail
                    {
                        DebtId = item.Id,
                        DebtRecordItemId = DebtRecordItem.Id,
                        AbatedValue = abatedValue,
                    };

                    if (item.Value > 0)
                    {
                        DebtRecordDetail.Value = item.Value - abatedValue;
                    }
                    else
                    {
                        DebtRecordDetail.Value = item.Value + abatedValue;
                    }

                    DebtRecordDetail.CreateBy(userName);
                    DebtRecordDetailList.Add(DebtRecordDetail);
                }
                else
                {
                    filteredCount++;
                }
            }

            _logger.LogInformation("应付盘点明细 筛选完成 - 公司: {CompanyId}, 总数据: {Total}, 有效明细: {Valid}, 已完全核销: {Filtered}",
                companyId, debtList.Count, DebtRecordDetailList.Count, filteredCount);

            try
            {
                excuteCount = await _debtRecordItemRepository.InsetrCredit(DebtRecordItem, DebtRecordDetailList);
                _logger.LogInformation("应付盘点数据 插入完成 - 公司: {CompanyId}, 影响行数: {ExecuteCount}, 盘点单号: {Code}",
                    companyId, excuteCount, code);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "应付盘点数据 插入失败 - 公司: {CompanyId}, 盘点单号: {Code}, 错误: {Message}",
                    companyId, code, ex.Message);
                throw;
            }

            return (excuteCount, code);
        }

        /// <summary>
        /// 付款盘点
        /// </summary>
        /// <param name="companyId">公司id</param>
        /// <param name="sysmonth">盘底月份</param>
        /// <param name="compnyCode">公司Code</param>
        /// <param name="Operator">中文名</param>
        /// <param name="userName">拼音名字</param>
        /// <returns></returns>
        public async Task<(int, string)> PaymentRecordInventory(Guid companyId, string sysmonth, string compnyCode, string compnyName, string userName)
        {
            int excuteCount = 0;
            string code = GetCodeInventory(compnyCode, "PR", sysmonth);

            _logger.LogInformation("开始付款盘点 - 公司: {CompanyId}, 月度: {SysMonth}, 盘点单号: {Code}", companyId, sysmonth, code);

            // 检查是否已存在相同的盘点记录
            var checkRepeat = await _queryPaymentRecordItem.FirstOrDefaultAsync(x => x.Code == code && x.CompanyId == companyId);
            if (checkRepeat != null)
            {
                _logger.LogInformation("付款盘点记录 已存在 - 公司: {CompanyId}, 盘点单号: {Code}", companyId, code);
                return (1, code);
            }

            // 查询符合条件的付款数据：未核销、有单号、非使用额度模式
            var payMentQuery = _queryPaymentPo.GetIQueryable(x => x.AbatedStatus == AbatedStatusEnum.NonAbate && x.CompanyId == companyId && !string.IsNullOrEmpty(x.Code) && x.AdvancePayMode != AdvancePayModeEnum.UseQuota);
            var paymentList = await payMentQuery.ToListAsync();

            _logger.LogInformation("查询到付款数据 - 公司: {CompanyId}, 数据量: {Count}", companyId, paymentList.Count);

            if (!paymentList.Any())
            {
                _logger.LogWarning("未找到符合条件的付款数据 - 公司: {CompanyId}, 跳过付款盘点 创建", companyId);
            }

            // 优化：智能选择查询策略，避免大量单号Contains查询
            var billCodeSet = paymentList.Where(x => !string.IsNullOrEmpty(x.Code))
                                        .Select(x => x.Code!)
                                        .ToHashSet();

            List<AbatmentDTO> abateMentDTOList;
            if (billCodeSet.Count <= 2000) // 小于2000个单号，直接查询
            {
                var abateMentQuery = _queryAbatementPo.GetIQueryable(x =>
                    billCodeSet.Contains(x.DebtBillCode) || billCodeSet.Contains(x.CreditBillCode));
                var abateMentList = await abateMentQuery.ToListAsync();
                abateMentDTOList = abateMentList.Select(p => p.Adapt<AbatmentDTO>()).ToList();
            }
            else // 大量单号，使用分批查询
            {
                var abateMentLookup = await QueryAbatementInBatchesAsync(billCodeSet.ToList());
                abateMentDTOList = abateMentLookup.SelectMany(g => g).ToList();
            }

            _logger.LogInformation("查询到核销数据 - 公司: {CompanyId}, 数据量: {Count}", companyId, abateMentDTOList.Count);

            // 创建盘点单头
            var PaymentRecordItem = new PaymentRecordItem
            {
                Id = Guid.NewGuid(),
                BillDate = GetBillDateInventory(sysmonth),
                Code = code,
                CompanyId = companyId,
                CompanyName = compnyName,
                UserName = userName,
                Operator = ""
            };
            PaymentRecordItem.CreateBy(userName);

            // 创建盘点明细
            List<PaymentRecordDetail> PaymentRecordDetailList = new List<PaymentRecordDetail>();

            foreach (var item in paymentList)
            {
                var abateMentItem = abateMentDTOList.Where(x => x.DebtBillCode == item.Code || x.CreditBillCode == item.Code).ToList();
                var abatedValue = abateMentItem.Sum(t => t.Value);
                var PaymentRecordDetail = new PaymentRecordDetail
                {
                    PaymentId = item.Id,
                    PaymentRecordItemId = PaymentRecordItem.Id,
                    AbatedValue = abatedValue,
                };

                if (item.Value > 0)
                {
                    PaymentRecordDetail.Value = item.Value - abatedValue;
                }
                else
                {
                    PaymentRecordDetail.Value = item.Value + abatedValue;
                }

                PaymentRecordDetail.CreateBy(userName);
                PaymentRecordDetailList.Add(PaymentRecordDetail);
            }

            _logger.LogInformation("创建付款盘点 明细 - 公司: {CompanyId}, 明细数量: {Count}", companyId, PaymentRecordDetailList.Count);

            try
            {
                excuteCount = await _paymentRecordItemRepository.InsetrCredit(PaymentRecordItem, PaymentRecordDetailList);
                _logger.LogInformation("付款盘点数据 插入完成 - 公司: {CompanyId}, 影响行数: {ExecuteCount}, 盘点单号: {Code}",
                    companyId, excuteCount, code);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "付款盘点数据 插入失败 - 公司: {CompanyId}, 盘点单号: {Code}, 错误: {Message}",
                    companyId, code, ex.Message);
                throw;
            }

            return (excuteCount, code);
        }
        /// <summary>
        /// 垫资盘点
        /// </summary>
        /// <param name="companyId">公司id</param>
        /// <param name="sysmonth">盘底月份</param>
        /// <param name="compnyCode">公司Code</param>
        /// <param name="compnyName">中文名</param>
        /// <param name="userName">拼音名字</param>
        /// <returns></returns>
        public async Task<(int, string)> AdvanceRecordInventory(Guid companyId, string sysmonth, string compnyCode, string compnyName, string userName)
        {
            int excuteCount = 0;
            string code = GetCodeInventory(compnyCode, "AR", sysmonth);

            _logger.LogInformation("开始垫资盘点 - 公司: {CompanyId}, 月度: {SysMonth}, 盘点单号: {Code}", companyId, sysmonth, code);

            // 检查是否已存在相同的盘点记录
            var checkRepeat = await _queryAdvanceRecordItem.FirstOrDefaultAsync(x => x.Code == code && x.CompanyId == companyId);
            if (checkRepeat != null)
            {
                _logger.LogInformation("垫资盘点记录 已存在 - 公司: {CompanyId}, 盘点单号: {Code}", companyId, code);
                return (1, code);
            }

            // 创建盘点单头
            var advanceRecordItem = new AdvanceFundBusinessCheckItem
            {
                Id = Guid.NewGuid(),
                BillDate = GetBillDateInventory(sysmonth),
                Code = code,
                CompanyId = companyId,
                CompanyName = compnyName,
                UserName = userName,
                Operator = ""
            };
            advanceRecordItem.CreateBy(userName);

            // 明细集合
            var AdvanceRecordDetailList = new List<AdvanceFundBusinessCheckDetail>();

            try
            {
                _logger.LogInformation("开始从集成平台获取垫资数据 - 公司: {CompanyId}", companyId);

                // 从集成平台中获取数据
                var advanceCheckDetails = await _iCApiClient.ICGetAdvanceCheckDetail(new DTOs.IC.AdvanceCheckDetailInput { companyId = companyId.ToString() });

                if (advanceCheckDetails.Code != CodeStatusEnum.Success)
                {
                    var errorMessage = $"集成平台接口调用失败: {advanceCheckDetails.Message}";
                    _logger.LogError("垫资盘点集成平台接口调用失败 - 公司: {CompanyId}, 错误: {Message}", companyId, advanceCheckDetails.Message);
                    throw new Exception(errorMessage);
                }

                if (advanceCheckDetails.Data?.List == null || !advanceCheckDetails.Data.List.Any())
                {
                    _logger.LogWarning("集成平台未返回垫资数据 - 公司: {CompanyId}, 跳过垫资盘点 创建", companyId);
                }

                _logger.LogInformation("从集成平台获取到垫资数据 - 公司: {CompanyId}, 数据量: {Count}", companyId, advanceCheckDetails.Data.List.Count);

                // 处理集成平台返回的数据
                foreach (var item in advanceCheckDetails.Data.List)
                {
                    try
                    {
                        var record = new AdvanceFundBusinessCheckDetail
                        {
                            Id = Guid.NewGuid(),
                            AdvanceBusinessApplyCode = item.AdvanceCode ?? "",
                            ServiceId = Guid.Parse(item.businessUnitId),
                            ServiceName = item.businessUnitName,
                            HospitalId = Guid.Parse(item.HospitalId),
                            HospitalName = item.HospitalName ?? "",
                            IsCheckedHospital = item.IsVerify == "是" ? 1 : 0,
                            IsProcessAllMage = item.IsTakeOver == "是" ? 1 : 0,
                            SCFDiscount = item.FinanceDiscount ?? 0,
                            RateOfYear = item.RateOfYear,
                            TotalDiscounts = item.TotalDiscount ?? 0,
                            AccountPeriod = item.providPayDays ?? 0,
                            ReceivePeriod = item.returnMoneyDays ?? 0,
                            Discount = item.costDiscount ?? 0,
                            BaseDiscount = item.distributionDiscount ?? 0,
                            SPDDiscount = item.spdDiscount ?? 0,
                            ADFDiscount = item.advanceRatio ?? 0,
                            CreditCode = item.creditCode ?? "",
                            CreditDate = item.creditDate.HasValue ? item.creditDate.Value : DateTime.MinValue,
                            CreditValue = item.creditBillValue.HasValue ? item.creditBillValue.Value : 0,
                            InvoiceDate = item.invoiceDate,
                            DebtCode = item.debtCode ?? "",
                            DebtDate = item.debtDate,
                            DebtValue = item.debtValue ?? 0,
                            ReceiveCode = item.receiveCode ?? "",
                            ReceiveDate = item.receiveDate,
                            ExpectReceiveDate = item.plannedReceiveDate,
                            PaymentCode = item.paymentCode ?? "",
                            PaymentDate = item.paymentDate,
                            ExpectPaymentDate = item.estimatePayDate,
                            CreditId = Guid.Parse(item.creditId),
                            DebtDetailId = Guid.NewGuid(),
                            AdvanceFundBusinessDays = 0,
                            SalesTaxRate = 0,
                            AdvanceFundBusinessCheckItemId = advanceRecordItem.Id,
                            EarlyReturnInterest = item.earlyReturnInterest,
                            OverdueInterest = item.overdueInterest,
                            ActualFinanceDiscount = item.actualFinanceDiscount,
                            ActualPaydays = item.actualPaydays,
                            AdvanceDays = item.actualPaydays,
                            AdvanceExpireTime = item.advanceExpireTime,
                            FundUsedValue = item.fundUsedValue,
                            BasicGrossProfit = item.basicGrossProfit,
                            HintRisk = item.hintRisk,
                            IntrestIncome = item.intrestIncome,
                            OverdueDays = item.overdueDays,
                            OverdueStatus = item.overdueStatus,
                            TotalGrossProfit = item.totalGrossProfit,
                            Verify = item.verify,
                            AgentName = item.agentName,
                            BatchpaymentCode = item.batchpaymentCode
                        };
                        AdvanceRecordDetailList.Add(record);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "垫资盘点明细数据转换失败 - 公司: {CompanyId}, 垫资代码: {AdvanceCode}, 错误: {Message}",
                            companyId, item.AdvanceCode, ex.Message);
                        // 继续处理其他数据，不中断整个流程
                    }
                }

                _logger.LogInformation("垫资盘点明细数据处理完成 - 公司: {CompanyId}, 有效明细数量: {Count}", companyId, AdvanceRecordDetailList.Count);

                if (!AdvanceRecordDetailList.Any())
                {
                    _logger.LogWarning("垫资盘点明细数据为空 - 公司: {CompanyId}", companyId);
                    return (0, code);
                }

                // 插入数据
                excuteCount = await _advanceRecordItemRepository.InsetrAdvance(advanceRecordItem, AdvanceRecordDetailList);
                _logger.LogInformation("垫资盘点数据 插入完成 - 公司: {CompanyId}, 影响行数: {ExecuteCount}, 盘点单号: {Code}",
                    companyId, excuteCount, code);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "垫资盘点 处理失败 - 公司: {CompanyId}, 盘点单号: {Code}, 错误: {Message}",
                    companyId, code, ex.Message);
                throw;
            }

            return (excuteCount, code);
        }
        /// <summary>
        /// 获取盘点Code
        /// </summary>
        /// <param name="companCode"></param>
        /// <param name="type">CR:应收，DR:应付 PR:付款</param>
        /// <param name="sysmonth"></param>
        /// <returns></returns>
        public string GetCodeInventory(string companCode, string type, string sysmonth)
        {
            string yearMonth = sysmonth.Split("-")[0].Substring(sysmonth.Split("-")[0].Length - 2) + sysmonth.Split("-")[1];
            string Code = companCode + "-" + type + "-" + yearMonth + "-" + "001";
            return Code;
        }


        /// <summary>
        /// 分批查询核销数据，避免大量单号导致查询超时
        /// </summary>
        /// <param name="billCodes">单号列表</param>
        /// <returns>核销数据查找表</returns>
        private async Task<ILookup<string, AbatmentDTO>> QueryAbatementInBatchesAsync(List<string> billCodes)
        {
            var allAbateMentList = new List<AbatmentDTO>();
            const int batchSize = 1000; // 每批处理1000个单号

            // 将单号分批处理
            for (int i = 0; i < billCodes.Count; i += batchSize)
            {
                var currentBatch = billCodes.Skip(i).Take(batchSize).ToList();

                try
                {
                    _logger.LogInformation("执行分批核销查询 - 批次: {BatchIndex}, 当前批次单号数量: {CurrentBatchSize}",
                        (i / batchSize) + 1, currentBatch.Count);

                    var batchQuery = _queryAbatementPo.GetIQueryable(x =>
                        currentBatch.Contains(x.DebtBillCode) || currentBatch.Contains(x.CreditBillCode));

                    var batchResult = await batchQuery.ToListAsync();
                    var batchDTOList = batchResult.Select(p => p.Adapt<AbatmentDTO>()).ToList();

                    allAbateMentList.AddRange(batchDTOList);

                    _logger.LogInformation("分批核销查询完成 - 批次: {BatchIndex}, 查询到数据: {ResultCount}条",
                        (i / batchSize) + 1, batchDTOList.Count);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "分批核销查询失败 - 批次: {BatchIndex}, 错误: {Message}",
                        (i / batchSize) + 1, ex.Message);
                    // 继续处理下一批，不因为单批失败而终止整个流程
                }
            }

            _logger.LogInformation("分批核销查询全部完成 - 总查询结果: {TotalCount}条", allAbateMentList.Count);

            // 构建查找表
            return allAbateMentList
                .SelectMany(x => new[]
                {
                    new { BillCode = x.DebtBillCode, Abatement = x },
                    new { BillCode = x.CreditBillCode, Abatement = x }
                })
                .Where(x => !string.IsNullOrEmpty(x.BillCode))
                .ToLookup(x => x.BillCode, x => x.Abatement);
        }

        /// <summary>
        /// 获取盘点BillDate
        /// </summary>
        /// <returns></returns>
        public DateTime GetBillDateInventory(string sysmonth)
        {
            DateTime today = Convert.ToDateTime(sysmonth + "-01");
            DateTime lastDayOfMonth = new DateTime(today.Year, today.Month, DateTime.DaysInMonth(today.Year, today.Month));
            string lastDayOfMonthString = lastDayOfMonth.ToString("yyyy-MM-dd");
            return Convert.ToDateTime(lastDayOfMonthString);
        }
        /// <inheritdoc/>
        public async Task<BaseResponseData<int>> FinishInventory(Guid inventoryItemId, string? userName = null)
        {
            var query = _inventoryQueryRep.GetIQueryable(p => p.Id == inventoryItemId);
            var item = await query.FirstOrDefaultAsync();
            if (item == null || item.Status == 99)
            {
                return BaseResponseData<int>.Failed(500, "总盘点单不存在或者已完成，无法进行此操作");
            }
            //if (string.IsNullOrEmpty(item.TempStore) || string.IsNullOrEmpty(item.Operation) || string.IsNullOrEmpty(item.Exchange))
            if (string.IsNullOrEmpty(item.TempStore) || string.IsNullOrEmpty(item.Exchange) || string.IsNullOrEmpty(item.CreditRecordCode) ||
                string.IsNullOrEmpty(item.DebtRecordCode) || string.IsNullOrEmpty(item.SureIncomeCode) || string.IsNullOrEmpty(item.PaymentRecordCode) || string.IsNullOrEmpty(item.Operation))
            {
                return BaseResponseData<int>.Failed(500, "其他盘点单还未生成，无法进行此操作");
            }

            // 检查业务单据是否为"生成中"状态，如果是则不允许设定完成
            if (item.TempStore == "生成中" || item.Operation == "生成中" || item.Exchange == "生成中" ||
                item.SureIncomeCode == "生成中" || item.CreditRecordCode == "生成中" ||
                item.ReceivedNoInvoiceRecordCode == "生成中" || item.DebtRecordCode == "生成中" ||
                item.PaymentRecordCode == "生成中" || item.AdvanceRecordCode == "生成中")
            {
                return BaseResponseData<int>.Failed(500, "业务单据正在生成中，无法设定完成，请等待生成完成后再操作");
            }
            var sysYear = DateTime.Parse(item.SysMonth).Year;
            var sysMonth = DateTime.Parse(item.SysMonth).Month;
            var rebate = await _rebatProvisionItemQueryRep.FirstOrDefaultAsync(p =>
                                                        p.CompanyId == item.CompanyId &&
                                                        p.BillDate.Value.Year == sysYear &&
                                                        p.BillDate.Value.Month == sysMonth
                                                        );
            if (rebate != null)
            {
                if (rebate.Status != StatusEnum.Complate)
                {
                    return BaseResponseData<int>.Failed(500, "返利计提单未提交，无法进行此操作");
                }
            }

            var currentSysMonth = item.SysMonth; // 从当前盘点单获取系统月度
            var newMonth = DateTime.Parse(currentSysMonth + "-01").AddMonths(1).ToString("yyyy-MM"); // 自动计算下个月

            // 发布完成盘点事件，异步处理系统月度更新
            var finishInventoryEvent = new InventoryFinishEventDto
            {
                CompanyIds = new List<Guid> { item.CompanyId },
                SysMonth = currentSysMonth,
                NewSystemPeriod = newMonth,
                SystemPeriodUpdated = false // 单个盘点完成不更新系统月度，由CompleteInventoryCheck统一处理
            };

            try
            {
                // 发布单个盘点完成事件到消息队列，异步处理
                await _daprClient.PublishEventAsync(DomainConstants.Default_PubSubName, "finance-inventory-finishinventory", finishInventoryEvent);
                _logger.LogInformation($"发布单个盘点完成事件成功，盘点单ID: {inventoryItemId}, 公司ID: {item.CompanyId}");
                return BaseResponseData<int>.Success("盘点完成请求已提交，系统正在处理中");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"发布完成盘点事件失败，盘点单ID: {inventoryItemId}");
                return BaseResponseData<int>.Failed(500, "发布盘点完成事件失败，请稍后重试");
            }
        }

        public async Task<BaseResponseData<int>> DeleteStoreInventory(StoreInventoryDeleteInputDto dto)
        {
            var query = _inventoryQueryRep.GetIQueryable(p => p.Status < 2 && p.CompanyId == dto.CompanyId);
            var itemPo = await query.FirstOrDefaultAsync();
            if (itemPo == null)
            {
                return BaseResponseData<int>.Failed(0, "该公司总盘点单不存在或不在库存盘点状态");
            }
            var item = itemPo.Adapt<InventoryItem>();
            var store = item.Store;
            if (string.IsNullOrEmpty(store))
            {
                return BaseResponseData<int>.Failed(0, "没有库存盘点记录，无法删除");
            }
            else
            {
                var data = JsonConvert.DeserializeObject<List<StoreInventoryUpdateInputDto>>(store);
                var thisStore = data.Where(p => p.StoreCheckCode == dto.StoreCheckCode).FirstOrDefault();
                if (thisStore == null)
                {
                    return BaseResponseData<int>.Failed(0, "不存在对应单盘点单号，无法删除");
                }
                else
                {
                    data = data.Where(p => p.StoreCheckCode != dto.StoreCheckCode).ToList();
                    item.Store = JsonConvert.SerializeObject(data);
                    if (!data.Any())
                    {
                        item.Status = 0;
                    }
                    await _inventoryRepository.UpdateAsync(item);
                    return BaseResponseData<int>.Success("操作成功");
                }
            }
        }

        /// <summary>
        /// 是否盘点期间
        /// </summary>
        /// <param name="companyId"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<int>> InventoryCheck(Guid companyId)
        {
            var query = _inventoryQueryRep.GetIQueryable(p => p.CompanyId == companyId);
            var itemPo = await query.OrderByDescending(o => o.SysMonth).FirstOrDefaultAsync();
            if (itemPo != null && itemPo.Status == 2)
            {
                return BaseResponseData<int>.Failed(0, "校验失败");
            }
            return BaseResponseData<int>.Success(1, "校验通过");
        }

        /// <summary>
        /// 批量更新盘点单字段 - 使用 EF Core 变更跟踪，解决并发问题
        /// </summary>
        /// <param name="inventoryItemId">盘点单ID</param>
        /// <param name="fieldUpdates">字段更新字典</param>
        /// <returns></returns>
        public async Task UpdateInventoryItemFields(Guid inventoryItemId, Dictionary<string, string> fieldUpdates)
        {
            if (fieldUpdates.Count == 0)
            {
                _logger.LogInformation("没有需要更新的字段，ID: {InventoryItemId}", inventoryItemId);
                return;
            }

            try
            {
                // 使用 Repository 的字段级别更新方法，利用 EF Core 变更跟踪
                var affectedRows = await _inventoryRepository.UpdateSpecificFields(inventoryItemId, fieldUpdates);

                if (affectedRows > 0)
                {
                    _logger.LogInformation("盘点单字段批量更新成功，ID: {InventoryItemId}, 更新字段数: {FieldCount}",
                        inventoryItemId, fieldUpdates.Count);
                }
                else
                {
                    _logger.LogWarning("盘点单字段更新失败，可能记录不存在，ID: {InventoryItemId}", inventoryItemId);
                    throw new InvalidOperationException($"盘点单不存在，ID: {inventoryItemId}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "盘点单字段批量更新失败，ID: {InventoryItemId}, 错误: {ErrorMessage}",
                    inventoryItemId, ex.Message);
                throw;
            }
        }



        /// <summary>
        /// 更新盘点单字段 - 单个字段更新（重构后使用统一的批量更新方法）
        /// </summary>
        /// <param name="inventoryItemId">盘点单ID</param>
        /// <param name="fieldName">字段名称</param>
        /// <param name="fieldValue">字段值</param>
        /// <returns></returns>
        public async Task UpdateInventoryItemField(Guid inventoryItemId, string fieldName, string fieldValue)
        {
            // 使用统一的批量更新方法，避免重复的字段映射逻辑
            var fieldUpdates = new Dictionary<string, string> { { fieldName, fieldValue } };
            await UpdateInventoryItemFields(inventoryItemId, fieldUpdates);
        }



        #region 第一阶段重构：提取验证逻辑（私有方法，零风险）

        /// <summary>
        /// 验证用户权限 - 从GetInventoryList方法中提取
        /// </summary>
        private async Task<bool> ValidateUserPermissionAsync(Guid userId, string functionUri)
        {
            try
            {
                var input = new StrategyQueryInput() { userId = userId, functionUri = functionUri };
                var strategy = await _pCApiClient.GetStrategyAsync(input);
                if (strategy != null)
                {
                    var rowStrategies = strategy.RowStrategies;
                    if (!rowStrategies.Keys.Contains("company"))
                    {
                        return false;
                    }
                }
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "验证用户权限失败，用户ID: {UserId}, 功能URI: {FunctionUri}", userId, functionUri);
                return false;
            }
        }

        /// <summary>
        /// 验证是否可以启动盘点 - 从StartInventory方法中提取
        /// </summary>
        private async Task<(bool IsValid, string ErrorMessage)> ValidateStartInventoryAsync(string sysMonth)
        {
            try
            {
                var existQuery = _inventoryQueryRep.GetIQueryable(p => p.SysMonth == sysMonth);
                var existingInventory = await existQuery.FirstOrDefaultAsync();
                if (existingInventory != null)
                {
                    // 根据盘点状态返回不同的提示信息
                    switch (existingInventory.Status)
                    {
                        case 0:
                            return (false, "本月盘点已创建，当前状态：待开始");
                        case 1:
                            return (false, "本月盘点已创建，当前状态：库存盘点中");
                        case 2:
                            return (false, "本月盘点已创建，当前状态：财务盘点中");
                        case 99:
                            return (false, "本月盘点已完成");
                        default:
                            return (false, "已存在本月的总盘点单");
                    }
                }

                // 保留原有的注释逻辑，便于后续恢复
                //var hasLossRecognitions = await _db.LossRecognitionItem.Where(p =>
                //                                p.Status == StatusEnum.waitAudit).AsNoTracking().ToListAsync();
                //if (hasLossRecognitions.Count > 0)
                //{
                //    return (false, "存在在途的损失确认申请单，无法开启盘点");
                //}

                return (true, string.Empty);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "验证启动盘点条件失败，系统月份: {SysMonth}", sysMonth);
                return (false, "验证失败，请稍后重试");
            }
        }

        /// <summary>
        /// 验证创建其他盘点的前置条件 - 从CreateOtherCheck方法中提取
        /// </summary>
        private async Task<(bool IsValid, string ErrorMessage)> ValidateCreateOtherCheckAsync(Guid companyId)
        {
            try
            {
                // 1. 检查盘点单是否存在
                var query = _inventoryQueryRep.GetIQueryable(p => p.CompanyId == companyId && p.Status != 99);
                var itemPo = await query.FirstOrDefaultAsync();
                if (itemPo == null)
                {
                    return (false, "该公司总盘点单不存在或不在库存盘点状态");
                }

                // 2. 检查库存盘点是否已开始
                var storeStr = itemPo.Store;
                if (string.IsNullOrEmpty(storeStr))
                {
                    return (false, "库存盘点未开始，不能进行此操作");
                }

                // 3. 检查库存盘点是否已完成
                var store = JsonConvert.DeserializeObject<List<StoreInventoryUpdateInputDto>>(storeStr);
                if (store.Any() && store.Where(p => p.StoreCheckStatus == true).Count() != store.FirstOrDefault().Total)
                {
                    return (false, "库存盘点未完成，不能进行此操作");
                }

                // 4. 验证金蝶系统状态
                var kingdeeValidation = await ValidateKingdeeStatusAsync(companyId);
                if (!kingdeeValidation.IsValid)
                {
                    return kingdeeValidation;
                }

                // 5. 检查损失确认申请单
                var hasLossRecognitions = await _db.LossRecognitionItem.Where(p =>
                                                p.Status == StatusEnum.waitAudit &&
                                                p.CompanyId == companyId).AsNoTracking().ToListAsync();
                if (hasLossRecognitions.Count > 0)
                {
                    return (false, "存在在途的损失确认申请单，无法开启盘点");
                }

                // 6. 检查提前垫资申请单
                var hasAdvancePaymentItems = await _db.AdvancePaymentItem.Where(p =>
                                                p.Status == AdvancePaymentStatusEnum.WaitAudit &&
                                                p.CompanyId == companyId).AsNoTracking().ToListAsync();
                if (hasAdvancePaymentItems.Count > 0)
                {
                    return (false, "存在在途的提前垫资申请单，无法开启盘点");
                }

                return (true, string.Empty);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "验证创建其他盘点条件失败，公司ID: {CompanyId}", companyId);
                return (false, "验证失败，请稍后重试");
            }
        }

        /// <summary>
        /// 验证金蝶系统状态 - 从CreateOtherCheck方法中提取
        /// </summary>
        private async Task<(bool IsValid, string ErrorMessage)> ValidateKingdeeStatusAsync(Guid companyId)
        {
            try
            {
                var companyList = await _applyBFFService.GetCompanyInfosAsync(new BDSBaseInput() { ids = new List<string> { companyId.ToString() } });
                if (!companyList.Any())
                {
                    return (false, "基础信息公司数据未获取到，不能进行此操作");
                }

                var kdInput = new NoPostBackSettleRequestVo()
                {
                    orgNumber = companyList.FirstOrDefault()?.NameCode,
                    endDate = DateTime.Now.ToString("yyyy-MM-dd")
                };
                var kdRet = await _kingdeeApiClient.QueryNoPostbackSettleBill(kdInput);
                if (kdRet.Code != CodeStatusEnum.Success)
                {
                    return (false, $"【金蝶】查询失败：{kdRet.Message}");
                }
                if (kdRet.Data != null && kdRet.Data.Any())
                {
                    string codes = string.Join(",", kdRet.Data.Select(x => x.number).ToList());
                    return (false, $"【金蝶】开启财务盘点失败！当前存在应收单【{codes}】未完成结算，请在认款单全部结算完成之后重试");
                }

                return (true, string.Empty);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "验证金蝶系统状态失败，公司ID: {CompanyId}", companyId);
                return (false, "验证金蝶系统状态失败");
            }
        }

        /// <summary>
        /// 批量查询应收单的冲销金额
        /// </summary>
        /// <param name="billCodes">应收单号集合</param>
        /// <returns>返回字典：应收单号 -> 冲销金额</returns>
        private async Task<Dictionary<string, decimal>> QueryAbatementAmountForCreditsAsync(IEnumerable<string> billCodes)
        {
            var result = new Dictionary<string, decimal>();

            if (billCodes == null || !billCodes.Any())
            {
                return result;
            }

            var billCodeList = billCodes.Where(x => !string.IsNullOrEmpty(x)).ToList();
            if (!billCodeList.Any())
            {
                return result;
            }

            try
            {
                // 批量查询冲销记录
                var abateMentQuery = _queryAbatementPo.GetIQueryable(x =>
                    billCodeList.Contains(x.DebtBillCode) || billCodeList.Contains(x.CreditBillCode));
                var abateMentList = await abateMentQuery.ToListAsync();
                var abateMentDTOList = abateMentList.Select(p => p.Adapt<AbatmentDTO>()).ToList();

                // 为每个应收单号计算冲销金额
                foreach (var billCode in billCodeList)
                {
                    var relatedAbatements = abateMentDTOList.Where(x =>
                        x.DebtBillCode == billCode || x.CreditBillCode == billCode);

                    // 使用简单的总和计算（与 CreditRecordInventory 保持一致）
                    var abatedValue = relatedAbatements.Sum(t => t.Value);
                    result[billCode] = abatedValue;
                }

                _logger.LogInformation("批量查询冲销金额完成 - 应收单数量: {CreditCount}, 冲销记录数量: {AbatementCount}",
                    billCodeList.Count, abateMentDTOList.Count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "批量查询冲销金额失败 - 应收单数量: {CreditCount}", billCodeList.Count);
                // 发生异常时返回空字典，调用方可以处理
            }

            return result;
        }

        /// <summary>
        /// 计算应收单余额（统一公式）
        /// </summary>
        /// <param name="creditValue">应收单金额</param>
        /// <param name="abatedValue">冲销金额</param>
        /// <returns>应收单余额</returns>
        private static decimal CalculateUnifiedCreditBalance(decimal creditValue, decimal abatedValue)
        {
            // 统一公式：余额 = 应收金额绝对值 - 冲销金额
            return Math.Abs(creditValue) - abatedValue;
        }

        #endregion
    }
}
